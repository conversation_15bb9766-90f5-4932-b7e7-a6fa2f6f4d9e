<template>
  <div class="basic-settings">
    <!-- 系统信息卡片 -->
    <div class="settings-card">
      <div class="card-header">
        <div class="header-content">
          <div class="header-icon">
            <el-icon size="20"><Setting /></el-icon>
          </div>
          <div class="header-text">
            <h3 class="card-title">系统信息</h3>
            <p class="card-subtitle">配置系统基本信息和标识</p>
          </div>
        </div>
      </div>

      <div class="card-body">
        <div class="settings-grid">
          <div class="setting-item">
            <div class="setting-header">
              <label class="setting-label">系统名称</label>
              <span class="setting-description">显示在页面标题和导航栏的系统名称</span>
            </div>
            <el-input
              v-model="systemInfo.name"
              placeholder="请输入系统名称"
              class="setting-input"
              size="large"
            />
          </div>

          <div class="setting-item">
            <div class="setting-header">
              <label class="setting-label">系统版本</label>
              <span class="setting-description">当前系统的版本号</span>
            </div>
            <el-input
              v-model="systemInfo.version"
              placeholder="请输入系统版本"
              class="setting-input"
              size="large"
            />
          </div>

          <div class="setting-item full-width">
            <div class="setting-header">
              <label class="setting-label">系统描述</label>
              <span class="setting-description">系统的详细描述信息</span>
            </div>
            <el-input
              v-model="systemInfo.description"
              type="textarea"
              :rows="4"
              placeholder="请输入系统描述"
              class="setting-input"
              resize="none"
            />
          </div>

          <div class="setting-item">
            <div class="setting-header">
              <label class="setting-label">联系邮箱</label>
              <span class="setting-description">系统管理员联系邮箱</span>
            </div>
            <el-input
              v-model="systemInfo.email"
              placeholder="请输入联系邮箱"
              class="setting-input"
              size="large"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 界面设置卡片 -->
    <div class="settings-card">
      <div class="card-header">
        <div class="header-content">
          <div class="header-icon">
            <el-icon size="20"><Brush /></el-icon>
          </div>
          <div class="header-text">
            <h3 class="card-title">界面设置</h3>
            <p class="card-subtitle">自定义系统外观和主题样式</p>
          </div>
        </div>
      </div>

      <div class="card-body">
        <div class="settings-grid">
          <div class="setting-item">
            <div class="setting-header">
              <label class="setting-label">主题色彩</label>
              <span class="setting-description">系统主要颜色主题</span>
            </div>
            <div class="color-picker-wrapper">
              <el-color-picker
                v-model="uiSettings.primaryColor"
                show-alpha
                size="large"
                class="setting-color-picker"
              />
              <span class="color-value">{{ uiSettings.primaryColor }}</span>
            </div>
          </div>

          <div class="setting-item">
            <div class="setting-header">
              <label class="setting-label">页面标题</label>
              <span class="setting-description">浏览器标签页显示的标题</span>
            </div>
            <el-input
              v-model="uiSettings.pageTitle"
              placeholder="请输入页面标题"
              class="setting-input"
              size="large"
            />
          </div>

          <div class="setting-item">
            <div class="setting-header">
              <label class="setting-label">Logo图片</label>
              <span class="setting-description">系统Logo，建议尺寸120x120px</span>
            </div>
            <div class="logo-upload-container">
              <el-upload
                class="logo-uploader"
                action="#"
                :show-file-list="false"
                :before-upload="beforeLogoUpload"
              >
                <div class="upload-content">
                  <img v-if="uiSettings.logoUrl" :src="uiSettings.logoUrl" class="logo-preview" />
                  <div v-else class="upload-placeholder">
                    <el-icon class="upload-icon"><Plus /></el-icon>
                    <span class="upload-text">点击上传Logo</span>
                  </div>
                </div>
              </el-upload>
            </div>
          </div>

          <div class="setting-item">
            <div class="setting-header">
              <label class="setting-label">侧边栏折叠</label>
              <span class="setting-description">默认侧边栏展开状态</span>
            </div>
            <div class="switch-wrapper">
              <el-switch
                v-model="uiSettings.sidebarCollapsed"
                active-text="默认折叠"
                inactive-text="默认展开"
                size="large"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 功能设置卡片 -->
    <div class="settings-card">
      <div class="card-header">
        <div class="header-content">
          <div class="header-icon">
            <el-icon size="20"><Tools /></el-icon>
          </div>
          <div class="header-text">
            <h3 class="card-title">功能设置</h3>
            <p class="card-subtitle">配置系统功能模块的开关状态</p>
          </div>
        </div>
      </div>

      <div class="card-body">
        <div class="feature-settings-grid">
          <div class="feature-item">
            <div class="feature-info">
              <div class="feature-icon">
                <el-icon size="18"><UserFilled /></el-icon>
              </div>
              <div class="feature-content">
                <h4 class="feature-title">用户注册</h4>
                <p class="feature-description">允许新用户自主注册账号</p>
              </div>
            </div>
            <el-switch
              v-model="featureSettings.allowRegistration"
              active-text="允许"
              inactive-text="禁止"
              size="large"
            />
          </div>

          <div class="feature-item">
            <div class="feature-info">
              <div class="feature-icon">
                <el-icon size="18"><Message /></el-icon>
              </div>
              <div class="feature-content">
                <h4 class="feature-title">邮件通知</h4>
                <p class="feature-description">系统事件邮件通知功能</p>
              </div>
            </div>
            <el-switch
              v-model="featureSettings.emailNotification"
              active-text="开启"
              inactive-text="关闭"
              size="large"
            />
          </div>

          <div class="feature-item">
            <div class="feature-info">
              <div class="feature-icon">
                <el-icon size="18"><FolderOpened /></el-icon>
              </div>
              <div class="feature-content">
                <h4 class="feature-title">数据备份</h4>
                <p class="feature-description">自动定期备份系统数据</p>
              </div>
            </div>
            <el-switch
              v-model="featureSettings.autoBackup"
              active-text="自动"
              inactive-text="手动"
              size="large"
            />
          </div>

          <div class="feature-item">
            <div class="feature-info">
              <div class="feature-icon">
                <el-icon size="18"><Warning /></el-icon>
              </div>
              <div class="feature-content">
                <h4 class="feature-title">维护模式</h4>
                <p class="feature-description">开启后用户无法访问系统</p>
              </div>
            </div>
            <el-switch
              v-model="featureSettings.maintenanceMode"
              active-text="开启"
              inactive-text="关闭"
              size="large"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="settings-actions">
      <div class="actions-container">
        <el-button type="primary" size="large" @click="saveSettings" :loading="saving">
          <el-icon><Check /></el-icon>
          保存设置
        </el-button>
        <el-button size="large" @click="resetSettings">
          <el-icon><RefreshLeft /></el-icon>
          重置设置
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Plus,
  Check,
  RefreshLeft,
  Setting,
  Brush,
  Tools,
  UserFilled,
  Message,
  FolderOpened,
  Warning
} from '@element-plus/icons-vue'

// 系统信息
const systemInfo = reactive({
  name: '智索管理平台',
  version: '1.0.0',
  description: '智能搜索与分析管理系统',
  email: '<EMAIL>'
})

// 界面设置
const uiSettings = reactive({
  primaryColor: '#722ED1',
  pageTitle: '智索管理平台',
  logoUrl: '',
  sidebarCollapsed: false
})

// 功能设置
const featureSettings = reactive({
  allowRegistration: true,
  emailNotification: true,
  autoBackup: false,
  maintenanceMode: false
})

const saving = ref(false)

// Logo上传前验证
const beforeLogoUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('Logo图片只能是 JPG/PNG 格式!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('Logo图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 保存设置
const saveSettings = async () => {
  saving.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('设置保存成功')
  } catch (error) {
    ElMessage.error('设置保存失败')
  } finally {
    saving.value = false
  }
}

// 重置设置
const resetSettings = () => {
  systemInfo.name = '智索管理平台'
  systemInfo.version = '1.0.0'
  systemInfo.description = '智能搜索与分析管理系统'
  systemInfo.email = '<EMAIL>'
  
  uiSettings.primaryColor = '#722ED1'
  uiSettings.pageTitle = '智索管理平台'
  uiSettings.logoUrl = ''
  uiSettings.sidebarCollapsed = false
  
  featureSettings.allowRegistration = true
  featureSettings.emailNotification = true
  featureSettings.autoBackup = false
  featureSettings.maintenanceMode = false
  
  ElMessage.success('设置已重置')
}
</script>

<style lang="scss" scoped>
.basic-settings {
  max-width: 1200px;
  margin: 0 auto;

  .settings-card {
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 32px;
    overflow: hidden;
    border: 1px solid #f0f2f5;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
      transform: translateY(-2px);
    }

    .card-header {
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      padding: 24px 32px;
      border-bottom: 1px solid #e2e8f0;

      .header-content {
        display: flex;
        align-items: center;
        gap: 16px;

        .header-icon {
          width: 48px;
          height: 48px;
          background: linear-gradient(135deg, #722ED1 0%, #8B5CF6 100%);
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          box-shadow: 0 4px 12px rgba(114, 46, 209, 0.3);
        }

        .header-text {
          .card-title {
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
            margin: 0 0 4px 0;
          }

          .card-subtitle {
            font-size: 14px;
            color: #64748b;
            margin: 0;
            line-height: 1.5;
          }
        }
      }
    }

    .card-body {
      padding: 32px;
    }
  }

  .settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 24px;

    .setting-item {
      &.full-width {
        grid-column: 1 / -1;
      }

      .setting-header {
        margin-bottom: 12px;

        .setting-label {
          font-size: 15px;
          font-weight: 600;
          color: #374151;
          margin-bottom: 4px;
          display: block;
        }

        .setting-description {
          font-size: 13px;
          color: #6b7280;
          line-height: 1.4;
        }
      }

      .setting-input {
        width: 100%;

        :deep(.el-input__wrapper) {
          border-radius: 10px;
          border: 2px solid #e5e7eb;
          transition: all 0.3s ease;

          &:hover {
            border-color: #722ED1;
          }

          &.is-focus {
            border-color: #722ED1;
            box-shadow: 0 0 0 3px rgba(114, 46, 209, 0.1);
          }
        }

        :deep(.el-textarea__inner) {
          border-radius: 10px;
          border: 2px solid #e5e7eb;
          transition: all 0.3s ease;

          &:hover {
            border-color: #722ED1;
          }

          &:focus {
            border-color: #722ED1;
            box-shadow: 0 0 0 3px rgba(114, 46, 209, 0.1);
          }
        }
      }

      .color-picker-wrapper {
        display: flex;
        align-items: center;
        gap: 12px;

        .setting-color-picker {
          :deep(.el-color-picker__trigger) {
            width: 48px;
            height: 48px;
            border-radius: 10px;
            border: 2px solid #e5e7eb;
            transition: all 0.3s ease;

            &:hover {
              border-color: #722ED1;
            }
          }
        }

        .color-value {
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 14px;
          color: #6b7280;
          background: #f8fafc;
          padding: 8px 12px;
          border-radius: 8px;
          border: 1px solid #e5e7eb;
        }
      }

      .switch-wrapper {
        display: flex;
        align-items: center;

        :deep(.el-switch) {
          .el-switch__core {
            border-radius: 20px;
            height: 24px;

            &.is-checked {
              background-color: #722ED1;
            }
          }
        }
      }
    }
  }

  .logo-upload-container {
    .logo-uploader {
      :deep(.el-upload) {
        border: 2px dashed #d1d5db;
        border-radius: 12px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;
        width: 120px;
        height: 120px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #fafbfc;

        &:hover {
          border-color: #722ED1;
          background: #f8f4ff;
        }
      }

      .upload-content {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;

        .logo-preview {
          width: 100%;
          height: 100%;
          object-fit: contain;
          border-radius: 8px;
        }

        .upload-placeholder {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 8px;

          .upload-icon {
            font-size: 24px;
            color: #9ca3af;
          }

          .upload-text {
            font-size: 12px;
            color: #6b7280;
            text-align: center;
          }
        }
      }
    }
  }

  .feature-settings-grid {
    display: grid;
    gap: 20px;

    .feature-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px;
      background: #f8fafc;
      border-radius: 12px;
      border: 1px solid #e2e8f0;
      transition: all 0.3s ease;

      &:hover {
        background: #f1f5f9;
        border-color: #cbd5e1;
      }

      .feature-info {
        display: flex;
        align-items: center;
        gap: 16px;
        flex: 1;

        .feature-icon {
          width: 40px;
          height: 40px;
          background: linear-gradient(135deg, #722ED1 0%, #8B5CF6 100%);
          border-radius: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          box-shadow: 0 2px 8px rgba(114, 46, 209, 0.2);
        }

        .feature-content {
          .feature-title {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin: 0 0 4px 0;
          }

          .feature-description {
            font-size: 13px;
            color: #64748b;
            margin: 0;
            line-height: 1.4;
          }
        }
      }

      :deep(.el-switch) {
        .el-switch__core {
          border-radius: 20px;
          height: 24px;

          &.is-checked {
            background-color: #722ED1;
          }
        }
      }
    }
  }

  .settings-actions {
    margin-top: 40px;

    .actions-container {
      display: flex;
      justify-content: center;
      gap: 16px;
      padding: 32px;
      background: #f8fafc;
      border-radius: 16px;
      border: 1px solid #e2e8f0;

      :deep(.el-button) {
        border-radius: 10px;
        font-weight: 600;
        padding: 12px 32px;
        transition: all 0.3s ease;

        &.el-button--primary {
          background: linear-gradient(135deg, #722ED1 0%, #8B5CF6 100%);
          border-color: #722ED1;
          box-shadow: 0 4px 12px rgba(114, 46, 209, 0.3);

          &:hover {
            background: linear-gradient(135deg, #6D28D9 0%, #7C3AED 100%);
            border-color: #6D28D9;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(114, 46, 209, 0.4);
          }
        }

        &:not(.el-button--primary) {
          background: white;
          border-color: #d1d5db;
          color: #374151;

          &:hover {
            border-color: #722ED1;
            color: #722ED1;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .basic-settings {
    .settings-card {
      margin-bottom: 20px;
      border-radius: 12px;

      .card-header {
        padding: 20px 24px;

        .header-content {
          gap: 12px;

          .header-icon {
            width: 40px;
            height: 40px;
          }

          .header-text {
            .card-title {
              font-size: 18px;
            }
          }
        }
      }

      .card-body {
        padding: 24px;
      }
    }

    .settings-grid {
      grid-template-columns: 1fr;
      gap: 20px;
    }

    .feature-settings-grid {
      .feature-item {
        padding: 16px;

        .feature-info {
          gap: 12px;

          .feature-icon {
            width: 36px;
            height: 36px;
          }

          .feature-content {
            .feature-title {
              font-size: 15px;
            }

            .feature-description {
              font-size: 12px;
            }
          }
        }
      }
    }

    .settings-actions {
      .actions-container {
        flex-direction: column;
        padding: 24px;

        :deep(.el-button) {
          width: 100%;
        }
      }
    }
  }
}
</style>
