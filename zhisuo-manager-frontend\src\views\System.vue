<template>
  <div class="system-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="header-icon">
            <el-icon size="24"><Setting /></el-icon>
          </div>
          <div class="header-text">
            <h2 class="page-title">系统设置</h2>
            <p class="page-description">管理系统配置、权限和安全设置</p>
          </div>
        </div>
        <div class="header-right">
          <div class="status-indicator">
            <div class="status-dot"></div>
            <span class="status-text">系统运行正常</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 标签页内容 -->
    <div class="content-container">
      <el-tabs v-model="activeTab" class="system-tabs" @tab-click="handleTabClick">
        <el-tab-pane name="basic">
          <template #label>
            <div class="tab-label">
              <el-icon><Setting /></el-icon>
              <span>基本设置</span>
            </div>
          </template>
          <BasicSettings />
        </el-tab-pane>

        <el-tab-pane name="admin">
          <template #label>
            <div class="tab-label">
              <el-icon><UserFilled /></el-icon>
              <span>管理员与权限</span>
            </div>
          </template>
          <AdminPermissions />
        </el-tab-pane>

        <el-tab-pane name="security">
          <template #label>
            <div class="tab-label">
              <el-icon><Lock /></el-icon>
              <span>安全配置</span>
            </div>
          </template>
          <SecurityConfig />
        </el-tab-pane>

        <el-tab-pane name="logs">
          <template #label>
            <div class="tab-label">
              <el-icon><Document /></el-icon>
              <span>系统日志</span>
            </div>
          </template>
          <SystemLogs />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Setting, UserFilled, Lock, Document } from '@element-plus/icons-vue'
import BasicSettings from './system/BasicSettings.vue'
import AdminPermissions from './system/AdminPermissions.vue'
import SecurityConfig from './system/SecurityConfig.vue'
import SystemLogs from './system/SystemLogs.vue'

// 当前激活的标签页
const activeTab = ref('basic')

// 处理标签页切换
const handleTabClick = (tab) => {
  console.log('切换到标签页:', tab.props.name)
}
</script>

<style lang="scss" scoped>
.system-page {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;

  .page-header {
    margin-bottom: 32px;

    .header-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 32px 0;

      .header-left {
        display: flex;
        align-items: center;
        gap: 20px;

        .header-icon {
          width: 56px;
          height: 56px;
          background: linear-gradient(135deg, #722ED1 0%, #8B5CF6 100%);
          border-radius: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          box-shadow: 0 8px 24px rgba(114, 46, 209, 0.3);
        }

        .header-text {
          .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1e293b;
            margin: 0 0 8px 0;
            background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
          }

          .page-description {
            color: #64748b;
            font-size: 16px;
            margin: 0;
            line-height: 1.5;
          }
        }
      }

      .header-right {
        .status-indicator {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 12px 20px;
          background: #f0fdf4;
          border: 1px solid #bbf7d0;
          border-radius: 12px;

          .status-dot {
            width: 8px;
            height: 8px;
            background: #22c55e;
            border-radius: 50%;
            animation: pulse 2s infinite;
          }

          .status-text {
            font-size: 14px;
            color: #166534;
            font-weight: 500;
          }
        }
      }
    }
  }

  .content-container {
    background: #ffffff;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    border: 1px solid #f1f5f9;
  }

  .system-tabs {
    :deep(.el-tabs__header) {
      margin: 0;
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      border-bottom: 1px solid #e2e8f0;
      padding: 0 32px;
    }

    :deep(.el-tabs__nav-wrap) {
      &::after {
        display: none;
      }
    }

    :deep(.el-tabs__item) {
      height: 64px;
      line-height: 64px;
      padding: 0 24px;
      font-size: 15px;
      font-weight: 500;
      color: #64748b;
      border: none;
      border-radius: 12px 12px 0 0;
      margin-right: 8px;
      transition: all 0.3s ease;
      position: relative;

      .tab-label {
        display: flex;
        align-items: center;
        gap: 8px;

        .el-icon {
          font-size: 16px;
        }
      }

      &:hover {
        color: #722ED1;
        background: rgba(114, 46, 209, 0.05);
      }

      &.is-active {
        color: #722ED1;
        background: #ffffff;
        font-weight: 600;
        box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);

        &::before {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 32px;
          height: 3px;
          background: linear-gradient(135deg, #722ED1 0%, #8B5CF6 100%);
          border-radius: 2px;
        }
      }
    }

    :deep(.el-tabs__content) {
      padding: 0;
      min-height: 600px;
      background: #ffffff;
    }
  }
}

// 动画效果
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .system-page {
    padding: 0 16px;

    .page-header {
      margin-bottom: 24px;

      .header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
        padding: 24px 0;

        .header-left {
          gap: 16px;

          .header-icon {
            width: 48px;
            height: 48px;
          }

          .header-text {
            .page-title {
              font-size: 28px;
            }

            .page-description {
              font-size: 14px;
            }
          }
        }
      }
    }

    .content-container {
      border-radius: 16px;
    }

    .system-tabs {
      :deep(.el-tabs__header) {
        padding: 0 20px;
      }

      :deep(.el-tabs__item) {
        height: 56px;
        line-height: 56px;
        padding: 0 16px;
        font-size: 14px;
        margin-right: 4px;

        .tab-label {
          gap: 6px;

          .el-icon {
            font-size: 14px;
          }
        }
      }
    }
  }
}
</style>
