<template>
  <div class="system-logs">
    <!-- 日志管理卡片 -->
    <div class="logs-card">
      <div class="card-header">
        <div class="header-content">
          <div class="header-icon">
            <el-icon size="20"><Document /></el-icon>
          </div>
          <div class="header-text">
            <h3 class="card-title">系统日志管理</h3>
            <p class="card-subtitle">查看和管理系统运行日志、操作记录和错误信息</p>
          </div>
        </div>
        <div class="header-actions">
          <el-button type="primary" size="large" @click="exportLogs">
            <el-icon><Download /></el-icon>
            导出日志
          </el-button>
          <el-button size="large" @click="clearLogs">
            <el-icon><Delete /></el-icon>
            清理日志
          </el-button>
        </div>
      </div>

      <!-- 日志类型标签页 -->
      <div class="log-tabs">
        <div class="tab-nav">
          <div
            v-for="tab in logTabs"
            :key="tab.key"
            :class="['tab-item', { active: activeLogTab === tab.key }]"
            @click="activeLogTab = tab.key"
          >
            <el-icon>
              <component :is="tab.icon" />
            </el-icon>
            <span>{{ tab.label }}</span>
            <el-badge v-if="tab.count" :value="tab.count" class="tab-badge" />
          </div>
        </div>
      </div>

      <!-- 筛选和搜索 -->
      <div class="log-filters">
        <div class="filter-section">
          <div class="filter-group">
            <label class="filter-label">时间范围</label>
            <el-date-picker
              v-model="dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              size="large"
              class="date-picker"
            />
          </div>

          <div class="filter-group">
            <label class="filter-label">日志级别</label>
            <el-select v-model="selectedLevel" placeholder="选择级别" size="large" class="level-select">
              <el-option label="全部级别" value="" />
              <el-option label="信息" value="info" />
              <el-option label="警告" value="warning" />
              <el-option label="错误" value="error" />
              <el-option label="调试" value="debug" />
            </el-select>
          </div>

          <div class="filter-group">
            <label class="filter-label">系统模块</label>
            <el-select v-model="selectedModule" placeholder="选择模块" size="large" class="module-select">
              <el-option label="全部模块" value="" />
              <el-option label="用户管理" value="user" />
              <el-option label="文章管理" value="article" />
              <el-option label="系统设置" value="system" />
              <el-option label="安全认证" value="auth" />
            </el-select>
          </div>

          <div class="filter-group">
            <label class="filter-label">搜索内容</label>
            <el-input
              v-model="searchKeyword"
              placeholder="搜索日志内容、用户或操作"
              prefix-icon="Search"
              clearable
              size="large"
              class="search-input"
            />
          </div>

          <div class="filter-actions">
            <el-button type="primary" size="large" @click="refreshLogs">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </div>

      <!-- 日志列表 -->
      <div class="log-table">
        <el-table
          :data="filteredLogs"
          style="width: 100%"
          :header-cell-style="{ background: '#f8f9fa', color: '#374151' }"
          row-key="id"
          :default-sort="{ prop: 'timestamp', order: 'descending' }"
        >
          <el-table-column prop="timestamp" label="时间" width="180" sortable>
            <template #default="{ row }">
              <span class="log-time">{{ formatTime(row.timestamp) }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="level" label="级别" width="80">
            <template #default="{ row }">
              <el-tag :type="getLevelTagType(row.level)" size="small">
                {{ getLevelText(row.level) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="module" label="模块" width="120">
            <template #default="{ row }">
              <span class="log-module">{{ getModuleText(row.module) }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="user" label="用户" width="120">
            <template #default="{ row }">
              <span class="log-user">{{ row.user || '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="action" label="操作" width="150">
            <template #default="{ row }">
              <span class="log-action">{{ row.action }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="message" label="详细信息" min-width="200">
            <template #default="{ row }">
              <div class="log-message">
                <p class="message-text">{{ row.message }}</p>
                <el-button
                  v-if="row.details"
                  type="text"
                  size="small"
                  @click="showLogDetails(row)"
                >
                  查看详情
                </el-button>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="ip" label="IP地址" width="130">
            <template #default="{ row }">
              <span class="log-ip">{{ row.ip || '-' }}</span>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[20, 50, 100, 200]"
            :total="totalCount"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- 日志详情对话框 -->
    <el-dialog
      v-model="showDetailsDialog"
      title="日志详情"
      width="600px"
      :before-close="() => showDetailsDialog = false"
    >
      <div v-if="selectedLog" class="log-details">
        <div class="detail-item">
          <label>时间：</label>
          <span>{{ formatTime(selectedLog.timestamp) }}</span>
        </div>
        <div class="detail-item">
          <label>级别：</label>
          <el-tag :type="getLevelTagType(selectedLog.level)">
            {{ getLevelText(selectedLog.level) }}
          </el-tag>
        </div>
        <div class="detail-item">
          <label>模块：</label>
          <span>{{ getModuleText(selectedLog.module) }}</span>
        </div>
        <div class="detail-item">
          <label>用户：</label>
          <span>{{ selectedLog.user || '-' }}</span>
        </div>
        <div class="detail-item">
          <label>操作：</label>
          <span>{{ selectedLog.action }}</span>
        </div>
        <div class="detail-item">
          <label>IP地址：</label>
          <span>{{ selectedLog.ip || '-' }}</span>
        </div>
        <div class="detail-item">
          <label>消息：</label>
          <p class="detail-message">{{ selectedLog.message }}</p>
        </div>
        <div v-if="selectedLog.details" class="detail-item">
          <label>详细信息：</label>
          <pre class="detail-code">{{ selectedLog.details }}</pre>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Document,
  Warning,
  CircleClose,
  Tools,
  Search,
  Refresh,
  Download,
  Delete
} from '@element-plus/icons-vue'

// 日志类型标签页
const logTabs = [
  { key: 'all', label: '全部日志', icon: 'Document', count: 0 },
  { key: 'operation', label: '操作日志', icon: 'Tools', count: 0 },
  { key: 'error', label: '错误日志', icon: 'CircleClose', count: 0 },
  { key: 'security', label: '安全日志', icon: 'Warning', count: 0 }
]

const activeLogTab = ref('all')
const dateRange = ref([])
const selectedLevel = ref('')
const selectedModule = ref('')
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalCount = ref(0)
const showDetailsDialog = ref(false)
const selectedLog = ref(null)

// 模拟日志数据
const logList = ref([
  {
    id: 1,
    timestamp: '2023-12-01 10:30:22',
    level: 'info',
    module: 'user',
    user: '张三',
    action: '用户登录',
    message: '用户张三成功登录系统',
    ip: '*************',
    details: null
  },
  {
    id: 2,
    timestamp: '2023-12-01 10:25:15',
    level: 'warning',
    module: 'auth',
    user: '李四',
    action: '密码错误',
    message: '用户李四登录密码错误，剩余尝试次数：2',
    ip: '*************',
    details: 'Login attempt failed for user: <EMAIL>'
  },
  {
    id: 3,
    timestamp: '2023-12-01 10:20:08',
    level: 'error',
    module: 'system',
    user: null,
    action: '数据库连接',
    message: '数据库连接超时',
    ip: null,
    details: 'Connection timeout after 30 seconds\nDatabase: mysql://localhost:3306/zhisuo\nError code: 1045'
  },
  {
    id: 4,
    timestamp: '2023-12-01 10:15:33',
    level: 'info',
    module: 'article',
    user: '王五',
    action: '文章发布',
    message: '用户王五发布了新文章《AI技术发展趋势》',
    ip: '*************',
    details: null
  },
  {
    id: 5,
    timestamp: '2023-12-01 10:10:45',
    level: 'debug',
    module: 'system',
    user: null,
    action: '系统启动',
    message: '系统服务启动完成',
    ip: null,
    details: 'All services started successfully\nMemory usage: 256MB\nCPU usage: 15%'
  }
])

// 过滤后的日志列表
const filteredLogs = computed(() => {
  let filtered = logList.value

  // 按标签页过滤
  if (activeLogTab.value !== 'all') {
    switch (activeLogTab.value) {
      case 'operation':
        filtered = filtered.filter(log => ['user', 'article'].includes(log.module))
        break
      case 'error':
        filtered = filtered.filter(log => log.level === 'error')
        break
      case 'security':
        filtered = filtered.filter(log => log.module === 'auth')
        break
    }
  }

  // 按级别过滤
  if (selectedLevel.value) {
    filtered = filtered.filter(log => log.level === selectedLevel.value)
  }

  // 按模块过滤
  if (selectedModule.value) {
    filtered = filtered.filter(log => log.module === selectedModule.value)
  }

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(log => 
      log.message.toLowerCase().includes(keyword) ||
      log.action.toLowerCase().includes(keyword) ||
      (log.user && log.user.toLowerCase().includes(keyword))
    )
  }

  // 按时间范围过滤
  if (dateRange.value && dateRange.value.length === 2) {
    const [startTime, endTime] = dateRange.value
    filtered = filtered.filter(log => 
      log.timestamp >= startTime && log.timestamp <= endTime
    )
  }

  totalCount.value = filtered.length
  const start = (currentPage.value - 1) * pageSize.value
  return filtered.slice(start, start + pageSize.value)
})

// 格式化时间
const formatTime = (timestamp) => {
  return timestamp
}

// 获取级别标签类型
const getLevelTagType = (level) => {
  switch (level) {
    case 'error': return 'danger'
    case 'warning': return 'warning'
    case 'info': return 'primary'
    case 'debug': return 'info'
    default: return 'info'
  }
}

// 获取级别文本
const getLevelText = (level) => {
  switch (level) {
    case 'error': return '错误'
    case 'warning': return '警告'
    case 'info': return '信息'
    case 'debug': return '调试'
    default: return level
  }
}

// 获取模块文本
const getModuleText = (module) => {
  switch (module) {
    case 'user': return '用户管理'
    case 'article': return '文章管理'
    case 'system': return '系统设置'
    case 'auth': return '安全认证'
    default: return module
  }
}

// 显示日志详情
const showLogDetails = (log) => {
  selectedLog.value = log
  showDetailsDialog.value = true
}

// 刷新日志
const refreshLogs = () => {
  ElMessage.success('日志已刷新')
}

// 导出日志
const exportLogs = () => {
  ElMessage.info('正在导出日志...')
  // 模拟导出
  setTimeout(() => {
    ElMessage.success('日志导出成功')
  }, 1000)
}

// 清理日志
const clearLogs = async () => {
  try {
    await ElMessageBox.confirm('确定要清理历史日志吗？此操作不可恢复。', '确认清理', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 模拟清理操作
    ElMessage.success('日志清理完成')
  } catch {
    ElMessage.info('已取消清理操作')
  }
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
}

const handleCurrentChange = (val) => {
  currentPage.value = val
}

// 更新标签页计数
const updateTabCounts = () => {
  logTabs[0].count = logList.value.length
  logTabs[1].count = logList.value.filter(log => ['user', 'article'].includes(log.module)).length
  logTabs[2].count = logList.value.filter(log => log.level === 'error').length
  logTabs[3].count = logList.value.filter(log => log.module === 'auth').length
}

onMounted(() => {
  totalCount.value = logList.value.length
  updateTabCounts()
})
</script>

<style lang="scss" scoped>
.system-logs {
  max-width: 1400px;
  margin: 0 auto;

  .logs-card {
    background: #ffffff;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    border: 1px solid #f1f5f9;

    .card-header {
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      padding: 32px;
      border-bottom: 1px solid #e2e8f0;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      .header-content {
        display: flex;
        align-items: center;
        gap: 16px;

        .header-icon {
          width: 48px;
          height: 48px;
          background: linear-gradient(135deg, #722ED1 0%, #8B5CF6 100%);
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          box-shadow: 0 4px 12px rgba(114, 46, 209, 0.3);
        }

        .header-text {
          .card-title {
            font-size: 24px;
            font-weight: 700;
            color: #1e293b;
            margin: 0 0 8px 0;
          }

          .card-subtitle {
            font-size: 15px;
            color: #64748b;
            margin: 0;
            line-height: 1.5;
          }
        }
      }

      .header-actions {
        display: flex;
        gap: 12px;

        :deep(.el-button) {
          border-radius: 10px;
          font-weight: 600;
          padding: 12px 24px;

          &.el-button--primary {
            background: linear-gradient(135deg, #722ED1 0%, #8B5CF6 100%);
            border-color: #722ED1;
            box-shadow: 0 4px 12px rgba(114, 46, 209, 0.3);

            &:hover {
              background: linear-gradient(135deg, #6D28D9 0%, #7C3AED 100%);
              transform: translateY(-2px);
              box-shadow: 0 6px 16px rgba(114, 46, 209, 0.4);
            }
          }

          &:not(.el-button--primary) {
            background: white;
            border-color: #d1d5db;
            color: #374151;

            &:hover {
              border-color: #ef4444;
              color: #ef4444;
              transform: translateY(-2px);
            }
          }
        }
      }
    }
  }

  .log-tabs {
    padding: 0 32px;
    margin-bottom: 32px;

    .tab-nav {
      display: flex;
      gap: 8px;

      .tab-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        color: #64748b;
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 10px;
        transition: all 0.3s ease;
        position: relative;

        .el-icon {
          font-size: 16px;
        }

        &:hover {
          color: #722ED1;
          background: #f3f0ff;
          border-color: #d8b4fe;
        }

        &.active {
          color: #722ED1;
          background: linear-gradient(135deg, #f3f0ff 0%, #ede9fe 100%);
          border-color: #722ED1;
          font-weight: 600;
          box-shadow: 0 2px 8px rgba(114, 46, 209, 0.2);
        }

        .tab-badge {
          :deep(.el-badge__content) {
            background: #722ED1;
            border-color: #722ED1;
          }
        }
      }
    }
  }

  .log-filters {
    padding: 0 32px;
    margin-bottom: 32px;

    .filter-section {
      background: #f8fafc;
      border-radius: 16px;
      padding: 24px;
      border: 1px solid #e2e8f0;

      .filter-group {
        margin-bottom: 20px;

        &:last-child {
          margin-bottom: 0;
        }

        .filter-label {
          display: block;
          font-size: 14px;
          font-weight: 600;
          color: #374151;
          margin-bottom: 8px;
        }

        .date-picker,
        .level-select,
        .module-select,
        .search-input {
          width: 100%;
          max-width: 350px;

          :deep(.el-input__wrapper) {
            border-radius: 10px;
            border: 2px solid #e5e7eb;
            transition: all 0.3s ease;

            &:hover {
              border-color: #722ED1;
            }

            &.is-focus {
              border-color: #722ED1;
              box-shadow: 0 0 0 3px rgba(114, 46, 209, 0.1);
            }
          }
        }
      }

      .filter-actions {
        display: flex;
        gap: 12px;
        justify-content: flex-end;

        :deep(.el-button) {
          border-radius: 10px;
          font-weight: 600;

          &.el-button--primary {
            background: linear-gradient(135deg, #722ED1 0%, #8B5CF6 100%);
            border-color: #722ED1;

            &:hover {
              background: linear-gradient(135deg, #6D28D9 0%, #7C3AED 100%);
              transform: translateY(-1px);
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .system-logs {
    .logs-card {
      border-radius: 16px;

      .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 20px;
        padding: 24px;

        .header-actions {
          width: 100%;
          justify-content: stretch;

          :deep(.el-button) {
            flex: 1;
          }
        }
      }
    }

    .log-tabs {
      padding: 0 24px;

      .tab-nav {
        flex-wrap: wrap;
        gap: 8px;

        .tab-item {
          flex: 1;
          min-width: calc(50% - 4px);
          justify-content: center;
        }
      }
    }

    .log-filters {
      padding: 0 24px;

      .filter-section {
        padding: 20px;

        .filter-group {
          .date-picker,
          .level-select,
          .module-select,
          .search-input {
            max-width: 100%;
          }
        }

        .filter-actions {
          justify-content: stretch;

          :deep(.el-button) {
            flex: 1;
          }
        }
      }
    }
  }
}
</style>
