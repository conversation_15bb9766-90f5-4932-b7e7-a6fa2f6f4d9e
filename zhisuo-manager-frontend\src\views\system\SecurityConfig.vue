<template>
  <div class="security-config">
    <!-- 密码策略卡片 -->
    <div class="config-card">
      <div class="card-header">
        <div class="header-content">
          <div class="header-icon">
            <el-icon size="20"><Lock /></el-icon>
          </div>
          <div class="header-text">
            <h3 class="card-title">密码策略</h3>
            <p class="card-subtitle">配置用户密码安全规则和策略</p>
          </div>
        </div>
      </div>

      <div class="card-body">
        <div class="config-grid">
          <div class="config-item">
            <div class="config-header">
              <label class="config-label">最小密码长度</label>
              <span class="config-desc">密码最少需要包含的字符数</span>
            </div>
            <el-input-number
              v-model="passwordPolicy.minLength"
              :min="6"
              :max="20"
              size="large"
              class="config-input"
            />
          </div>

          <div class="config-item">
            <div class="config-header">
              <label class="config-label">密码有效期</label>
              <span class="config-desc">密码过期天数，0表示永不过期</span>
            </div>
            <el-input-number
              v-model="passwordPolicy.expireDays"
              :min="0"
              :max="365"
              size="large"
              class="config-input"
            />
          </div>

          <div class="config-item">
            <div class="config-header">
              <label class="config-label">密码重试次数</label>
              <span class="config-desc">密码错误后锁定账户的最大尝试次数</span>
            </div>
            <el-input-number
              v-model="passwordPolicy.maxRetries"
              :min="3"
              :max="10"
              size="large"
              class="config-input"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 登录安全卡片 -->
    <div class="config-card">
      <div class="card-header">
        <div class="header-content">
          <div class="header-icon">
            <el-icon size="20"><Setting /></el-icon>
          </div>
          <div class="header-text">
            <h3 class="card-title">登录安全</h3>
            <p class="card-subtitle">配置用户登录相关的安全策略</p>
          </div>
        </div>
      </div>

      <div class="card-body">
        <div class="security-features-grid">
          <div class="security-feature">
            <div class="feature-info">
              <div class="feature-icon">
                <el-icon size="18"><Timer /></el-icon>
              </div>
              <div class="feature-content">
                <h4 class="feature-title">会话超时</h4>
                <p class="feature-description">用户无操作自动退出时间</p>
              </div>
            </div>
            <div class="feature-control">
              <el-input-number
                v-model="loginSecurity.sessionTimeout"
                :min="30"
                :max="1440"
                size="large"
                class="number-input"
              />
              <span class="unit-text">分钟</span>
            </div>
          </div>

          <div class="security-feature">
            <div class="feature-info">
              <div class="feature-icon">
                <el-icon size="18"><Shield /></el-icon>
              </div>
              <div class="feature-content">
                <h4 class="feature-title">双因子认证</h4>
                <p class="feature-description">启用后需要手机验证码或邮箱验证</p>
              </div>
            </div>
            <el-switch
              v-model="loginSecurity.twoFactorAuth"
              active-text="启用"
              inactive-text="禁用"
              size="large"
            />
          </div>

          <div class="security-feature">
            <div class="feature-info">
              <div class="feature-icon">
                <el-icon size="18"><Location /></el-icon>
              </div>
              <div class="feature-content">
                <h4 class="feature-title">IP白名单</h4>
                <p class="feature-description">只允许指定IP地址登录</p>
              </div>
            </div>
            <el-switch
              v-model="loginSecurity.ipWhitelist"
              active-text="启用"
              inactive-text="禁用"
              size="large"
            />
          </div>

          <div class="security-feature">
            <div class="feature-info">
              <div class="feature-icon">
                <el-icon size="18"><Picture /></el-icon>
              </div>
              <div class="feature-content">
                <h4 class="feature-title">登录验证码</h4>
                <p class="feature-description">登录时需要输入图形验证码</p>
              </div>
            </div>
            <el-switch
              v-model="loginSecurity.captcha"
              active-text="启用"
              inactive-text="禁用"
              size="large"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="config-actions">
      <div class="actions-container">
        <el-button type="primary" size="large" @click="saveConfig" :loading="saving">
          <el-icon><Check /></el-icon>
          保存配置
        </el-button>
        <el-button size="large" @click="resetConfig">
          <el-icon><RefreshLeft /></el-icon>
          重置配置
        </el-button>
        <el-button type="warning" size="large" @click="testSecurity">
          <el-icon><Monitor /></el-icon>
          安全测试
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Lock,
  Setting,
  Warning,
  Key,
  View,
  Check,
  RefreshLeft,
  Monitor,
  Timer,
  Shield,
  Location,
  Picture
} from '@element-plus/icons-vue'

// 密码策略配置
const passwordPolicy = reactive({
  minLength: 8,
  complexity: ['lowercase', 'numbers'],
  expireDays: 90,
  maxRetries: 5
})

// 登录安全配置
const loginSecurity = reactive({
  sessionTimeout: 120,
  twoFactorAuth: false,
  ipWhitelist: false,
  captcha: true
})

// 安全监控配置
const securityMonitor = reactive({
  abnormalLogin: true,
  operationLog: true,
  notifications: ['email', 'system'],
  logRetentionDays: 90
})

// 数据加密配置
const dataEncryption = reactive({
  httpsOnly: true,
  databaseEncryption: false,
  fileEncryption: false,
  algorithm: 'AES-256'
})

// 访问控制配置
const accessControl = reactive({
  apiRateLimit: 1000,
  corsEnabled: false,
  maxFileSize: 10,
  allowedFileTypes: 'jpg,png,gif,pdf,doc,docx,xls,xlsx'
})

const saving = ref(false)

// 保存配置
const saveConfig = async () => {
  saving.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('安全配置保存成功')
  } catch (error) {
    ElMessage.error('安全配置保存失败')
  } finally {
    saving.value = false
  }
}

// 重置配置
const resetConfig = () => {
  // 重置为默认值
  passwordPolicy.minLength = 8
  passwordPolicy.complexity = ['lowercase', 'numbers']
  passwordPolicy.expireDays = 90
  passwordPolicy.maxRetries = 5
  
  loginSecurity.sessionTimeout = 120
  loginSecurity.twoFactorAuth = false
  loginSecurity.ipWhitelist = false
  loginSecurity.captcha = true
  
  securityMonitor.abnormalLogin = true
  securityMonitor.operationLog = true
  securityMonitor.notifications = ['email', 'system']
  securityMonitor.logRetentionDays = 90
  
  dataEncryption.httpsOnly = true
  dataEncryption.databaseEncryption = false
  dataEncryption.fileEncryption = false
  dataEncryption.algorithm = 'AES-256'
  
  accessControl.apiRateLimit = 1000
  accessControl.corsEnabled = false
  accessControl.maxFileSize = 10
  accessControl.allowedFileTypes = 'jpg,png,gif,pdf,doc,docx,xls,xlsx'
  
  ElMessage.success('配置已重置为默认值')
}

// 安全测试
const testSecurity = async () => {
  try {
    ElMessage.info('正在进行安全测试...')
    // 模拟安全测试
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('安全测试通过，系统配置安全')
  } catch (error) {
    ElMessage.error('安全测试失败，请检查配置')
  }
}
</script>

<style lang="scss" scoped>
.security-config {
  max-width: 1200px;
  margin: 0 auto;

  .config-card {
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 32px;
    overflow: hidden;
    border: 1px solid #f0f2f5;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
      transform: translateY(-2px);
    }

    .card-header {
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      padding: 24px 32px;
      border-bottom: 1px solid #e2e8f0;

      .header-content {
        display: flex;
        align-items: center;
        gap: 16px;

        .header-icon {
          width: 48px;
          height: 48px;
          background: linear-gradient(135deg, #722ED1 0%, #8B5CF6 100%);
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          box-shadow: 0 4px 12px rgba(114, 46, 209, 0.3);
        }

        .header-text {
          .card-title {
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
            margin: 0 0 4px 0;
          }

          .card-subtitle {
            font-size: 14px;
            color: #64748b;
            margin: 0;
            line-height: 1.5;
          }
        }
      }
    }

    .card-body {
      padding: 32px;
    }
  }

  .config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 24px;

    .config-item {
      &.full-width {
        grid-column: 1 / -1;
      }

      .config-header {
        margin-bottom: 12px;

        .config-label {
          font-size: 15px;
          font-weight: 600;
          color: #374151;
          margin-bottom: 4px;
          display: block;
        }

        .config-desc {
          font-size: 13px;
          color: #6b7280;
          line-height: 1.4;
        }
      }

      .config-input {
        width: 100%;

        :deep(.el-input__wrapper) {
          border-radius: 10px;
          border: 2px solid #e5e7eb;
          transition: all 0.3s ease;

          &:hover {
            border-color: #722ED1;
          }

          &.is-focus {
            border-color: #722ED1;
            box-shadow: 0 0 0 3px rgba(114, 46, 209, 0.1);
          }
        }

        :deep(.el-input-number) {
          width: 100%;

          .el-input__wrapper {
            border-radius: 10px;
            border: 2px solid #e5e7eb;

            &:hover {
              border-color: #722ED1;
            }

            &.is-focus {
              border-color: #722ED1;
              box-shadow: 0 0 0 3px rgba(114, 46, 209, 0.1);
            }
          }
        }
      }

      .complexity-options {
        .config-checkbox-group {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 16px;

          .checkbox-item {
            :deep(.el-checkbox) {
              width: 100%;
              margin-right: 0;

              .el-checkbox__label {
                width: 100%;

                .checkbox-content {
                  display: flex;
                  align-items: center;
                  gap: 8px;
                  padding: 12px 16px;
                  background: #f8fafc;
                  border-radius: 10px;
                  border: 1px solid #e2e8f0;
                  transition: all 0.3s ease;

                  .el-icon {
                    color: #722ED1;
                  }

                  span {
                    font-size: 14px;
                    color: #374151;
                    font-weight: 500;
                  }
                }
              }

              &.is-checked {
                .checkbox-content {
                  background: #f3f0ff;
                  border-color: #722ED1;

                  span {
                    color: #722ED1;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .security-features-grid {
    display: grid;
    gap: 20px;

    .security-feature {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px;
      background: #f8fafc;
      border-radius: 12px;
      border: 1px solid #e2e8f0;
      transition: all 0.3s ease;

      &:hover {
        background: #f1f5f9;
        border-color: #cbd5e1;
      }

      .feature-info {
        display: flex;
        align-items: center;
        gap: 16px;
        flex: 1;

        .feature-icon {
          width: 40px;
          height: 40px;
          background: linear-gradient(135deg, #722ED1 0%, #8B5CF6 100%);
          border-radius: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          box-shadow: 0 2px 8px rgba(114, 46, 209, 0.2);
        }

        .feature-content {
          .feature-title {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin: 0 0 4px 0;
          }

          .feature-description {
            font-size: 13px;
            color: #64748b;
            margin: 0;
            line-height: 1.4;
          }
        }
      }

      .feature-control {
        display: flex;
        align-items: center;
        gap: 8px;

        .number-input {
          width: 120px;

          :deep(.el-input__wrapper) {
            border-radius: 8px;
          }
        }

        .unit-text {
          font-size: 14px;
          color: #6b7280;
          font-weight: 500;
        }
      }

      :deep(.el-switch) {
        .el-switch__core {
          border-radius: 20px;
          height: 24px;

          &.is-checked {
            background-color: #722ED1;
          }
        }
      }
    }
  }

  .config-actions {
    margin-top: 40px;

    .actions-container {
      display: flex;
      justify-content: center;
      gap: 16px;
      padding: 32px;
      background: #f8fafc;
      border-radius: 16px;
      border: 1px solid #e2e8f0;

      :deep(.el-button) {
        border-radius: 10px;
        font-weight: 600;
        padding: 12px 32px;
        transition: all 0.3s ease;

        &.el-button--primary {
          background: linear-gradient(135deg, #722ED1 0%, #8B5CF6 100%);
          border-color: #722ED1;
          box-shadow: 0 4px 12px rgba(114, 46, 209, 0.3);

          &:hover {
            background: linear-gradient(135deg, #6D28D9 0%, #7C3AED 100%);
            border-color: #6D28D9;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(114, 46, 209, 0.4);
          }
        }

        &.el-button--warning {
          background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
          border-color: #f59e0b;
          color: white;

          &:hover {
            background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
            transform: translateY(-2px);
          }
        }

        &:not(.el-button--primary):not(.el-button--warning) {
          background: white;
          border-color: #d1d5db;
          color: #374151;

          &:hover {
            border-color: #722ED1;
            color: #722ED1;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .security-config {
    .config-card {
      margin-bottom: 20px;
      border-radius: 12px;

      .card-header {
        padding: 20px 24px;

        .header-content {
          gap: 12px;

          .header-icon {
            width: 40px;
            height: 40px;
          }

          .header-text {
            .card-title {
              font-size: 18px;
            }
          }
        }
      }

      .card-body {
        padding: 24px;
      }
    }

    .config-grid {
      grid-template-columns: 1fr;
      gap: 20px;
    }

    .security-features-grid {
      .security-feature {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
        padding: 16px;

        .feature-control {
          width: 100%;
          justify-content: flex-end;
        }
      }
    }

    .config-actions {
      .actions-container {
        flex-direction: column;
        padding: 24px;

        :deep(.el-button) {
          width: 100%;
        }
      }
    }
  }
}
</style>
